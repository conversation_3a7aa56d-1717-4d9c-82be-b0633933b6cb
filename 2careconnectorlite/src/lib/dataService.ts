import { supabase } from './supabase'

// Clean, minimal data service using REAL tables from care_connector schema
export const dataService = {
  // Caregivers - using profiles table with role filtering (schema set in supabase config)
  async getCaregivers() {
    try {
      // Fetch verified caregivers from database
      const { data, error } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('*')
        .in('role', ['caregiver', 'companion', 'professional', 'care_checker'])
        .eq('is_verified', true)

      if (error) {
        console.error('Database error fetching caregivers:', error)
        return []
      }

      console.log('Raw caregiver data from database:', data)
      console.log('Number of caregivers found:', data?.length || 0)

      // Map database fields to expected interface - using actual database columns
      const mappedData = (data || []).map(profile => ({
        id: profile.id,
        name: profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim(),
        bio: profile.needs || 'Experienced caregiver dedicated to providing compassionate care',
        location: profile.location || 'Location not specified',
        specialties: profile.personality_traits || profile.languages || ['General Care'],
        verified: profile.is_verified || false,
        provider_type: 'caregiver',
        hourly_rate: profile.hourly_rate || null, // Use actual database value only
        years_experience: profile.years_of_experience || null,
        profile_image: profile.avatar_url,
        rating: parseFloat(profile.average_rating) || null,
        reviews_count: profile.reviews_count || null,
        availability_status: profile.points > 50 ? 'Available' : 'Busy' // Dynamic based on points
      }))

      console.log('Mapped caregiver data:', mappedData)
      return mappedData
    } catch (error) {
      console.error('Error in getCaregivers:', error)
      return []
    }
  },

  async getCaregiver(id: string) {
    const { data, error } = await supabase
      .from('care_connector.profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching caregiver:', error)
      throw error
    }

    // Map database fields to expected interface
    const mappedData = {
      id: data.id,
      name: data.full_name || `${data.first_name || ''} ${data.last_name || ''}`.trim(),
      bio: data.bio || null,
      location: data.location || null,
      specialties: data.specialties || null,
      verified: data.is_verified || false,
      provider_type: 'caregiver',
      hourly_rate: data.hourly_rate || null,
      years_experience: data.years_of_experience || null,
      profile_image: data.avatar_url,
      rating: parseFloat(data.average_rating) || null,
      reviews_count: data.reviews_count || null,
      availability: data.availability || null
    }

    return mappedData
  },

  // Companions - using profiles table with proper field mapping (schema set in supabase config)
  async getCompanions() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('*')
        .eq('role', 'companion')

      if (error) {
        console.error('Database error fetching companions:', error)
        throw error
      }

      // Map database fields to expected Companion interface
      const mappedData = (data || []).map(profile => ({
        id: profile.id,
        full_name: profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim(),
        bio: profile.interests?.join(', ') || 'Experienced companion providing emotional support and daily assistance',
        location: profile.location || 'Location not specified',
        specialties: profile.languages || ['General Support'],
        avatar_url: profile.avatar_url,
        average_rating: parseFloat(profile.average_rating) || null,
        reviews_count: profile.reviews_count || null,
        years_of_experience: profile.years_of_experience || null,
        hourly_rate: profile.hourly_rate || null, // Use actual database value only
        availability_status: 'Available'
      }))

      return mappedData
    } catch (error) {
      console.error('Error in getCompanions:', error)
      throw error
    }
  },

  // Professionals - using profiles table (schema set in supabase config)
  async getProfessionals() {
    const { data, error } = await supabase
      .schema('care_connector')
      .from('profiles')
      .select('*')
      .eq('role', 'professional')

    if (error) {
      console.error('Error fetching professionals:', error)
      throw error
    }
    
    // Map raw data to expected format (same as getCaregivers)
    return (data || []).map(professional => ({
      id: professional.id,
      name: professional.full_name,
      location: professional.location,
      hourlyRate: professional.hourly_rate,
      rating: professional.rating,
      reviews: professional.reviews,
      experience: professional.experience,
      specialties: professional.specialties || null,
      bio: professional.bio,
      verified: professional.verified,
      ...professional
    }))
  },

  // Care Checkers - using profiles table (schema set in supabase config)
  async getCareCheckers() {
    const { data, error } = await supabase
      .schema('care_connector')
      .from('profiles')
      .select('*')
      .in('role', ['caregiver', 'companion', 'professional', 'care_checker'])
      .eq('is_verified', true)

    if (error) {
      console.error('Error fetching care checkers:', error)
      throw error
    }
    
    // Map raw data to expected format (same as getCaregivers)
    return (data || []).map(careChecker => ({
      id: careChecker.id,
      name: careChecker.full_name,
      location: careChecker.location,
      hourlyRate: careChecker.hourly_rate,
      rating: careChecker.rating,
      reviews: careChecker.reviews,
      experience: careChecker.experience,
      specialties: careChecker.specialties || null,
      bio: careChecker.bio,
      verified: careChecker.verified,
      ...careChecker
    }))
  },

  // Care Groups - using actual care_groups table with real database data
  async getCareGroups() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('care_groups')
        .select('*')
        .eq('privacy_setting', 'public')
        .eq('is_archived', false)
        .order('created_at', { ascending: false })
        .limit(20)

      if (error) {
        console.error('Error fetching care groups:', error)
        throw error
      }

      // Map database fields to expected interface
      const mappedData = (data || []).map(group => ({
        id: group.id,
        name: group.name,
        description: group.description || '',
        privacy_setting: group.privacy_setting,
        member_count: group.member_count || 0,
        created_at: group.created_at,
        category_id: group.category_id,
        avatar_url: group.avatar_url,
        category: group.category_id || 'General',
        is_member: false
      }))

      return mappedData
    } catch (error) {
      console.error('Error in getCareGroups:', error)
      throw error
    }
  },

  // Homepage Statistics - fetch real data from database tables
  async getHomepageStats() {
    try {
      // Get total verified professionals count
      const { count: professionalsCount } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('id', { count: 'exact', head: true })
        .in('role', ['caregiver', 'companion', 'professional', 'care_checker'])
        .eq('is_verified', true)

      // Get average rating from reviews/ratings table
      const { data: ratingsData } = await supabase
        .schema('care_connector')
        .from('reviews')
        .select('rating')
        .not('rating', 'is', null)

      const averageRating = ratingsData && ratingsData.length > 0
        ? (ratingsData.reduce((sum, review) => sum + review.rating, 0) / ratingsData.length).toFixed(1)
        : null

      // Get total successful bookings count
      const { count: bookingsCount } = await supabase
        .schema('care_connector')
        .from('bookings')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'completed')

      // Calculate support status based on business hours and verified staff count
      const businessHours = {
        start: 8, // 8 AM
        end: 18   // 6 PM  
      }
      const currentHour = new Date().getHours()
      const isBusinessHours = currentHour >= businessHours.start && currentHour < businessHours.end
      const hasMinimumStaff = (professionalsCount || 0) >= 5
      
      let supportStatus = null
      if (hasMinimumStaff && isBusinessHours) {
        supportStatus = 'Available'
      } else if (hasMinimumStaff) {
        supportStatus = '24/7'
      } else {
        supportStatus = 'Online'
      }

      return {
        verifiedProfessionals: professionalsCount || 0,
        averageRating: averageRating,
        successfulBookings: bookingsCount || 0,
        supportStatus: supportStatus
      }
    } catch (error) {
      console.error('Error fetching homepage stats:', error)
      // Return error state values if database query fails - no fake data allowed
      return {
        verifiedProfessionals: 0,
        averageRating: null,
        successfulBookings: 0,
        supportStatus: null
      }
    }
  },

  // Dynamic Search Options - fetch from database to eliminate hardcoded dropdown options
  async getSearchFilterOptions() {
    try {
      // Get unique care types from profiles table
      const { data: careTypesData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('role')
        .not('role', 'is', null)

      const careTypes = [...new Set(careTypesData?.map(p => p.role) || [])]
        .map(role => ({ value: role, label: role.charAt(0).toUpperCase() + role.slice(1) }))

      // Get unique availability options from profiles table
      const { data: availabilityData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('availability')
        .not('availability', 'is', null)

      const availability = [...new Set(availabilityData?.flatMap(p => p.availability || []) || [])]
        .map(avail => ({ value: avail, label: avail }))

      // Get unique insurance types from profiles table
      const { data: insuranceData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('insurance_accepted')
        .not('insurance_accepted', 'is', null)

      const insurance = [...new Set(insuranceData?.flatMap(p => p.insurance_accepted || []) || [])]
        .map(ins => ({ value: ins, label: ins }))

      // Get unique languages from profiles table
      const { data: languagesData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('languages')
        .not('languages', 'is', null)

      const languages = [...new Set(languagesData?.flatMap(p => p.languages || []) || [])]
        .map(lang => ({ value: lang, label: lang }))

      // Get unique certifications from profiles table
      const { data: certificationsData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('certifications')
        .not('certifications', 'is', null)

      const certifications = [...new Set(certificationsData?.flatMap(p => p.certifications || []) || [])]
        .map(cert => ({ value: cert, label: cert }))

      return {
        careTypes: careTypes.length > 0 ? careTypes : [],
        availability: availability.length > 0 ? availability : [],
        insurance: insurance.length > 0 ? insurance : [],
        languages: languages.length > 0 ? languages : [],
        certifications: certifications.length > 0 ? certifications : []
      }
    } catch (error) {
      console.error('Error fetching search filter options:', error)
      // Return empty arrays if database query fails - no fake data allowed
      return {
        careTypes: [],
        availability: [],
        insurance: [],
        languages: [],
        certifications: []
      }
    }
  },

  // Get navigation options for Find Care dropdown from database
  getNavigationOptions: async function() {
    try {
      // Get available care provider types from profiles table
      const { data: providerTypes } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('role')
        .not('role', 'is', null)

      // Create unique navigation options from database data
      const uniqueRoles = [...new Set(providerTypes?.map(p => p.role) || [])]
      
      const navigationOptions = uniqueRoles.map(role => {
        // Map database roles to navigation structure
        switch (role.toLowerCase()) {
          case 'caregiver':
            return {
              title: 'Caregivers',
              description: 'Professional caregivers',
              route: '/caregivers',
              icon: 'Users'
            }
          case 'companion':
            return {
              title: 'Companions', 
              description: 'Companion care',
              route: '/companions',
              icon: 'Users'
            }
          case 'professional':
            return {
              title: 'Professionals',
              description: 'Healthcare professionals', 
              route: '/professionals',
              icon: 'Shield'
            }
          case 'care_checker':
            return {
              title: 'Care Checkers',
              description: 'Quality assurance',
              route: '/care-checkers', 
              icon: 'CheckSquare'
            }
          default:
            return {
              title: role.charAt(0).toUpperCase() + role.slice(1),
              description: `${role} services`,
              route: `/${role.toLowerCase()}`,
              icon: 'Users'
            }
        }
      })

      return navigationOptions
    } catch (error) {
      console.error('Error fetching navigation options:', error)
      // Return empty array on error to prevent hardcoded fallbacks
      return []
    }
  },

  getHowItWorksSteps: async function() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('how_it_works_steps')
        .select('*')
        .order('step_number')

      if (error) {
        console.error('Error fetching how-it-works steps:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching how-it-works steps:', error)
      return []
    }
  },

  // Notifications - fetch user notifications from database
  async getNotifications(userId: string) {
    try {
      console.log('Fetching notifications for user:', userId)
      const { data, error } = await supabase
        .schema('care_connector')
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Database error fetching notifications:', error)
        return []
      }

      console.log('Notifications found:', data?.length || 0)
      return data || []
    } catch (error) {
      console.error('❌ DATASERVICE: MAJOR ERROR in getNotifications:', error)
      console.error('❌ DATASERVICE: Error type:', typeof error)
      console.error('❌ DATASERVICE: Error message:', error instanceof Error ? error.message : String(error))
      console.error('❌ DATASERVICE: Error stack:', error instanceof Error ? error.stack : 'No stack')
      
      if (error instanceof Error && error.message.includes('timeout')) {
        console.error('❌ DATASERVICE: QUERY TIMEOUT - Supabase client may not be connecting properly')
      }
      
      return []
    }
  },

  async submitContactForm(formData: {
    name: string
    email: string
    subject: string
    message: string
    urgency: string
    submitted_at: string
  }) {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('contact_messages')
        .insert([
          {
            name: formData.name,
            email: formData.email,
            subject: formData.subject,
            message: formData.message,
            urgency: formData.urgency,
            submitted_at: formData.submitted_at,
            status: 'new'
          }
        ])
        .select()

      if (error) {
        console.error('Error submitting contact form:', error)
        throw error
      }

      console.log('Contact form submitted successfully:', data)
      return data
    } catch (error) {
      console.error('Error in submitContactForm:', error)
      throw error
    }
  }
}
