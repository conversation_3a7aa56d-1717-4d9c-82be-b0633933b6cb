import { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Search, Users, Sparkles, Calendar, MessageSquare, CheckSquare, Shield, UserCheck, ChevronDown, CheckCircle, Award } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function Home() {
  const [showFindCareDropdown, setShowFindCareDropdown] = useState(false)
  const [caregivers, setCaregivers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [homepageStats, setHomepageStats] = useState({
    verifiedProfessionals: 0,
    averageRating: null,
    successfulBookings: 0,
    supportStatus: null
  })
  const [searchFilters, setSearchFilters] = useState({
    careType: 'all',
    location: '',
    availability: 'any',
    insurance: 'any',
    language: 'any',
    certification: 'any'
  })
  const [searchFilterOptions, setSearchFilterOptions] = useState({
    careTypes: [],
    availability: [],
    insurance: [],
    languages: [],
    certifications: []
  })
  const [navigationOptions, setNavigationOptions] = useState<any[]>([])
  const navigate = useNavigate()

  useEffect(() => {
    const fetchData = async () => {
      try {
        setError(null) // Clear any previous errors
        console.log('Fetching homepage data...')

        // Fetch caregivers, homepage statistics, dynamic search filter options, and navigation options
        const [caregiversData, statsData, searchOptionsData, navigationOptionsData] = await Promise.all([
          dataService.getCaregivers(),
          dataService.getHomepageStats(),
          dataService.getSearchFilterOptions(),
          dataService.getNavigationOptions()
        ])

        console.log('Caregivers data received:', caregiversData)
        console.log('Homepage stats received:', statsData)

        setCaregivers(caregiversData.slice(0, 3)) // Show only first 3 for homepage
        setHomepageStats(statsData)
        setSearchFilterOptions(searchOptionsData)
        setNavigationOptions(navigationOptionsData)
      } catch (error) {
        console.error('Error fetching homepage data:', error)
        setError('Failed to load care providers. Please try again later.')
        setCaregivers([])
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleGetStarted = () => {
    navigate('/get-started')
  }

  const handleBrowseProviders = () => {
    navigate('/caregivers')
  }

  const handleSearchSubmit = () => {
    // Build search parameters from filters
    const searchParams = new URLSearchParams()

    if (searchFilters.careType !== 'all') {
      searchParams.set('type', searchFilters.careType)
    }

    if (searchFilters.location.trim()) {
      searchParams.set('location', searchFilters.location.trim())
    }

    if (searchFilters.availability !== 'any') {
      searchParams.set('availability', searchFilters.availability)
    }

    if (searchFilters.insurance !== 'any') {
      searchParams.set('insurance', searchFilters.insurance)
    }

    if (searchFilters.language !== 'any') {
      searchParams.set('language', searchFilters.language)
    }

    if (searchFilters.certification !== 'any') {
      searchParams.set('certification', searchFilters.certification)
    }

    // Navigate to caregivers page with search parameters
    const searchQuery = searchParams.toString()
    navigate(`/caregivers${searchQuery ? `?${searchQuery}` : ''}`)
  }

  const handleFilterChange = (filterType: string, value: string) => {
    setSearchFilters(prev => ({
      ...prev,
      [filterType]: value
    }))
  }

  const handleAIHelper = () => {
    // Navigate to AI assistance page or open AI chat
    navigate('/ai-assistant')
  }

  return (
    <main className="min-h-screen bg-primary">
      {/* Hero Section - Elegant & Minimal Design Inspired by Reference App */}
      <section className="px-8 py-32 text-center bg-primary relative overflow-hidden" role="banner" aria-label="Hero section">
        {/* Subtle Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-32 h-32 rounded-full bg-accent animate-pulse-subtle"></div>
          <div className="absolute top-40 right-32 w-24 h-24 rounded-full bg-accent animate-pulse-subtle" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-32 left-1/3 w-20 h-20 rounded-full bg-accent animate-pulse-subtle" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="max-w-5xl mx-auto relative z-10">
          {/* Main Title - Elegant & Sophisticated */}
          <div className="animate-fadeInUp">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-light mb-8 leading-tight tracking-tight text-primary max-w-4xl mx-auto">
              Your Complete Care Network
            </h1>
            <p className="text-lg sm:text-xl text-secondary max-w-2xl mx-auto leading-relaxed">
              Connect with verified healthcare professionals and build your personalized care team
            </p>
          </div>

          {/* Enhanced Subtitle with Staggered Animation */}
          <div className="mt-8 mb-12 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
            <div className="flex flex-wrap justify-center items-center gap-6 text-xl sm:text-2xl lg:text-3xl font-light text-primary">
              <span className="animate-slideInLeft" style={{animationDelay: '0.4s'}}>Trusted</span>
              <div className="w-2 h-2 rounded-full bg-accent animate-fadeInScale" style={{animationDelay: '0.6s'}}></div>
              <span className="animate-fadeInUp" style={{animationDelay: '0.8s'}}>Verified</span>
              <div className="w-2 h-2 rounded-full bg-accent animate-fadeInScale" style={{animationDelay: '1.0s'}}></div>
              <span className="animate-slideInRight" style={{animationDelay: '1.2s'}}>Connected</span>
            </div>
          </div>

          {/* Enhanced Description */}
          <div className="animate-fadeInUp" style={{animationDelay: '0.4s'}}>
            <p className="text-xl mb-16 max-w-3xl mx-auto leading-relaxed text-secondary font-light">
              Connect with certified healthcare professionals and build your comprehensive care support system with
              <span className="text-primary"> precision</span> and
              <span className="text-primary"> elegance</span>.
            </p>
          </div>
        </div>

        {/* Enhanced CTA Section */}
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fadeInUp" style={{animationDelay: '0.6s'}}>
          {/* Find Care Dropdown - Enhanced Primary CTA */}
          <div className="relative">
            <button
              onClick={() => setShowFindCareDropdown(!showFindCareDropdown)}
              className="group button-primary px-8 py-4 rounded-xl text-lg font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg flex items-center gap-3 min-w-[180px]"
              aria-label="Find care providers"
              aria-expanded={showFindCareDropdown}
            >
              <Search className="w-5 h-5 transition-transform group-hover:scale-110" />
              Find Care
              <ChevronDown className={`w-5 h-5 transition-all duration-300 ${showFindCareDropdown ? 'rotate-180' : ''} group-hover:translate-y-0.5`} />
            </button>

            {showFindCareDropdown && (
              <div
                className="absolute top-full left-1/2 transform -translate-x-1/2 mt-4 w-80 rounded-2xl shadow-2xl z-50 bg-primary border border-light backdrop-blur-sm animate-fadeInScale"
                style={{boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15)'}}
              >
                <div className="py-4">
                  {navigationOptions.map((option, index) => {
                    // Dynamic icon mapping based on icon string from database
                    const iconMap = {
                      'Users': Users,
                      'Shield': Shield,
                      'CheckSquare': CheckSquare,
                      'UserCheck': UserCheck,
                      'Search': Search,
                      'Calendar': Calendar,
                      'MessageSquare': MessageSquare,
                      'Sparkles': Sparkles
                    }
                    const IconComponent = iconMap[option.icon] || Users

                    return (
                      <Link
                        key={index}
                        to={option.route}
                        className="group block px-6 py-4 transition-all duration-200 hover:bg-secondary rounded-xl mx-2 transform hover:scale-[1.02]"
                        onClick={() => setShowFindCareDropdown(false)}
                      >
                        <div className="flex items-center gap-4">
                          <div className="p-2 rounded-lg bg-accent group-hover:bg-primary transition-colors">
                            <IconComponent className="w-5 h-5 text-primary group-hover:text-white transition-colors" />
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-primary group-hover:text-primary transition-colors">{option.title}</div>
                            <div className="text-sm text-secondary group-hover:text-secondary transition-colors leading-relaxed">{option.description}</div>
                          </div>
                        </div>
                      </Link>
                    )
                  })}
                </div>
              </div>
            )}
          </div>

          <Link
            to="/how-it-works"
            className="group px-8 py-4 rounded-xl text-lg font-medium transition-all duration-300 text-primary border-2 border-light no-underline hover:border-primary hover:bg-accent hover:shadow-lg transform hover:scale-105 flex items-center gap-3 min-w-[180px] justify-center"
          >
            <Sparkles className="w-5 h-5 transition-transform group-hover:rotate-12" />
            Learn More
          </Link>
        </div>
      </section>

      {/* Trust Indicators Section - Modern Enhanced - BRIGHT WHITE */}
      <section className="py-20 px-4 sm:px-8 bg-primary relative overflow-hidden">
        {/* Background Elements - Subtle */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-12 right-12 w-20 h-20 rounded-full bg-accent animate-pulse-subtle"></div>
          <div className="absolute bottom-12 left-12 w-16 h-16 rounded-full bg-primary animate-pulse-subtle" style={{animationDelay: '1.5s'}}></div>
        </div>

        <div className="max-w-6xl mx-auto relative z-10">
          {/* Section Header */}
          <div className="text-center mb-16 animate-fadeInUp">
            <h2 className="text-3xl sm:text-4xl font-light text-primary mb-4">
              Trusted by <span className="font-medium">{homepageStats.verifiedProfessionals ? (homepageStats.verifiedProfessionals >= 100 ? `${Math.floor(homepageStats.verifiedProfessionals / 100) * 100}+` : `${homepageStats.verifiedProfessionals}+`) : 'Thousands'}</span>
            </h2>
            <p className="text-lg text-secondary max-w-2xl mx-auto">
              Join a growing community of healthcare professionals and families who trust our platform
            </p>
          </div>

          {/* Enhanced Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {homepageStats.verifiedProfessionals !== undefined && (
              <div className="group text-center p-8 rounded-2xl bg-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-fadeInUp">
                <div className="text-4xl lg:text-5xl font-light mb-4 text-primary group-hover:scale-110 transition-transform duration-300">
                  {homepageStats.verifiedProfessionals.toLocaleString()}
                </div>
                <div className="text-sm font-medium text-secondary uppercase tracking-wide">Verified Professionals</div>
                <div className="mt-3 w-12 h-1 bg-accent rounded-full mx-auto group-hover:w-16 transition-all duration-300"></div>
              </div>
            )}
            {homepageStats.averageRating && (
              <div className="group text-center p-8 rounded-2xl bg-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-fadeInUp" style={{animationDelay: '0.1s'}}>
                <div className="text-4xl lg:text-5xl font-light mb-4 text-primary group-hover:scale-110 transition-transform duration-300">
                  {homepageStats.averageRating}★
                </div>
                <div className="text-sm font-medium text-secondary uppercase tracking-wide">Average Rating</div>
                <div className="mt-3 w-12 h-1 bg-accent rounded-full mx-auto group-hover:w-16 transition-all duration-300"></div>
              </div>
            )}
            {homepageStats.successfulBookings !== undefined && (
              <div className="group text-center p-8 rounded-2xl bg-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
                <div className="text-4xl lg:text-5xl font-light mb-4 text-primary group-hover:scale-110 transition-transform duration-300">
                  {homepageStats.successfulBookings.toLocaleString()}
                </div>
                <div className="text-sm font-medium text-secondary uppercase tracking-wide">Successful Bookings</div>
                <div className="mt-3 w-12 h-1 bg-accent rounded-full mx-auto group-hover:w-16 transition-all duration-300"></div>
              </div>
            )}
            {homepageStats.supportStatus && (
              <div className="group text-center p-8 rounded-2xl bg-primary shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-fadeInUp" style={{animationDelay: '0.3s'}}>
                <div className="text-4xl lg:text-5xl font-light mb-4 text-primary group-hover:scale-110 transition-transform duration-300">
                  {homepageStats.supportStatus}
                </div>
                <div className="text-sm font-medium text-secondary uppercase tracking-wide">Support Available</div>
                <div className="mt-3 w-12 h-1 bg-accent rounded-full mx-auto group-hover:w-16 transition-all duration-300"></div>
              </div>
            )}
          </div>

          {/* Enhanced Security Badges */}
          <div className="flex flex-wrap items-center justify-center gap-6 animate-fadeInUp" style={{animationDelay: '0.4s'}}>
            <div className="group flex items-center gap-3 px-6 py-4 rounded-xl shadow-lg bg-primary border border-light hover:border-accent hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="p-2 rounded-lg bg-accent group-hover:bg-primary transition-colors">
                <CheckCircle className="w-5 h-5 text-primary group-hover:text-white transition-colors" />
              </div>
              <span className="text-sm font-semibold text-primary">HIPAA Compliant</span>
            </div>
            <div className="group flex items-center gap-3 px-6 py-4 rounded-xl shadow-lg bg-primary border border-light hover:border-accent hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="p-2 rounded-lg bg-accent group-hover:bg-primary transition-colors">
                <Shield className="w-5 h-5 text-primary group-hover:text-white transition-colors" />
              </div>
              <span className="text-sm font-semibold text-primary">Background Verified</span>
            </div>
            <div className="group flex items-center gap-3 px-6 py-4 rounded-xl shadow-lg bg-primary border border-light hover:border-accent hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="p-2 rounded-lg bg-accent group-hover:bg-primary transition-colors">
                <Award className="w-5 h-5 text-primary group-hover:text-white transition-colors" />
              </div>
              <span className="text-sm font-semibold text-primary">Licensed & Insured</span>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Search Section - Elegant & Minimal */}
      <section className="py-24 px-4 sm:px-8 bg-primary">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-light mb-6 text-primary">
              Find Your Perfect Care Match
            </h2>
            <p className="text-lg text-secondary max-w-2xl mx-auto leading-relaxed">
              Search by location, specialty, availability, and more
            </p>
          </div>

          <form onSubmit={(e) => { e.preventDefault(); handleSearchSubmit(); }} className="rounded-2xl p-10 bg-primary border border-light shadow-sm hover:shadow-md transition-all duration-300" role="search" aria-label="Find care providers">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              <div>
                <label htmlFor="care-type-select" className="block text-sm font-medium mb-2 text-primary">
                  What type of care?
                </label>
                <select
                  value={searchFilters.careType}
                  onChange={(e) => handleFilterChange('careType', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 focus:border-primary bg-primary border-light text-primary transition-all duration-200 hover:border-medium"
                  aria-label="Select care type"
                  id="care-type-select"
                >
                  <option value="all">What care do you need?</option>
                  {searchFilterOptions.careTypes.map(careType => (
                    <option key={careType.value} value={careType.value}>
                      {careType.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="location-input" className="block text-sm font-medium mb-2 text-primary">
                  Location
                </label>
                <input
                  id="location-input"
                  type="text"
                  placeholder="City, state, or 12345"
                  value={searchFilters.location}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  required
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 focus:border-primary bg-primary border-light text-primary transition-all duration-200 hover:border-medium placeholder-styled"
                />
              </div>

              <div>
                <label htmlFor="availability-select" className="block text-sm font-medium mb-2 text-primary">
                  Availability
                </label>
                <select
                  id="availability-select"
                  value={searchFilters.availability}
                  onChange={(e) => handleFilterChange('availability', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 focus:border-primary bg-primary border-light text-primary transition-all duration-200 hover:border-medium"
                >
                  <option value="any">When do you need care?</option>
                  {searchFilterOptions.availability.map(avail => (
                    <option key={avail.value} value={avail.value}>
                      {avail.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="insurance-select" className="block text-sm font-medium mb-2 text-primary">
                  Insurance Accepted
                </label>
                <select
                  id="insurance-select"
                  value={searchFilters.insurance}
                  onChange={(e) => handleFilterChange('insurance', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-20 focus:border-primary bg-primary border-light text-primary transition-all duration-200 hover:border-medium"
                >
                  <option value="any">Select your insurance</option>
                  {searchFilterOptions.insurance.map(ins => (
                    <option key={ins.value} value={ins.value}>
                      {ins.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="language-select" className="block text-sm font-medium mb-2 text-primary">
                  Languages Spoken
                </label>
                <select
                  id="language-select"
                  value={searchFilters.language}
                  onChange={(e) => handleFilterChange('language', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)',
                    '--tw-ring-color': 'var(--primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">Preferred language</option>
                  {searchFilterOptions.languages.map(lang => (
                    <option key={lang.value} value={lang.value}>
                      {lang.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="certification-select" className="block text-sm font-medium mb-2 text-primary">
                  Certifications
                </label>
                <select
                  id="certification-select"
                  value={searchFilters.certification}
                  onChange={(e) => handleFilterChange('certification', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)',
                    '--tw-ring-color': 'var(--primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">Required certifications</option>
                  {searchFilterOptions.certifications.map(cert => (
                    <option key={cert.value} value={cert.value}>
                      {cert.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="text-center">
              <button
                onClick={(e) => { e.preventDefault(); handleSearchSubmit(); }}
                className="button-primary w-full px-6 py-3 rounded-lg text-base font-medium macos-button-elegant focus:outline-none focus:ring-2 focus:ring-opacity-50"
                aria-label="Search for care providers"
                type="button"
              >
                <Search className="w-5 h-5 inline mr-3" />
                Search Care Providers
              </button>
            </div>
          </form>
        </div>
      </section>

      {/* Your Complete Care Network Section */}
      <section
        className="py-24 px-4 sm:px-8 text-center"
        style={{ backgroundColor: 'var(--bg-primary)' }}
      >
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl sm:text-4xl font-light mb-8 text-primary">
            Your Complete Care Network
          </h2>
          <p className="text-lg mb-20 max-w-3xl mx-auto text-secondary leading-relaxed">
            Healthcare coordination for modern families with comprehensive tools and verified professionals.
          </p>
        </div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Find Verified Care */}
          <div
            className="rounded-lg p-8 shadow-sm border"
            style={{
              backgroundColor: 'var(--bg-primary)',
              borderColor: 'var(--border-light)',
              color: 'var(--text-primary)'
            }}
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
              style={{ backgroundColor: 'var(--accent)' }}
            >
              <Search className="w-8 h-8" style={{ color: 'var(--text-primary)' }} />
            </div>
            <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              Find Verified Care
            </h3>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>
              Connect with verified healthcare professionals who deliver exceptional, compassionate care.
            </p>
            <button
              onClick={handleBrowseProviders}
              className="w-full px-6 py-3 rounded-lg font-medium flex items-center justify-center gap-2 macos-button-elegant border"
              style={{
                backgroundColor: 'var(--bg-primary)',
                borderColor: 'var(--border-medium)',
                color: 'var(--text-primary)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
              }}
            >
              Browse Providers
              <Search className="w-4 h-4" />
            </button>
          </div>

          {/* Build Your Care Group */}
          <div
            className="rounded-lg p-8 shadow-sm border"
            style={{
              backgroundColor: 'var(--bg-primary)',
              borderColor: 'var(--border-light)',
              color: 'var(--text-primary)'
            }}
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
              style={{ backgroundColor: 'var(--accent)' }}
            >
              <Users className="w-8 h-8" style={{ color: 'var(--text-primary)' }} />
            </div>
            <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              Build Your Care Group
            </h3>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>
              Coordinate care seamlessly between family members, caregivers, and healthcare professionals.
            </p>
            <Link
              to="/how-it-works"
              className="inline-flex items-center gap-1.5 no-underline text-sm font-medium transition-all duration-200 px-3 py-2 rounded-md"
              style={{ color: 'var(--primary)' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}
            >
              New Group →
            </Link>
          </div>

          {/* AI-Powered Assistance */}
          <div
            className="rounded-lg p-8 shadow-sm border"
            style={{
              backgroundColor: 'var(--bg-primary)',
              borderColor: 'var(--border-light)',
              color: 'var(--text-primary)'
            }}
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
              style={{ backgroundColor: 'var(--accent)' }}
            >
              <Sparkles className="w-8 h-8" style={{ color: 'var(--text-primary)' }} />
            </div>
            <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              AI-Powered Assistance
            </h3>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>
              Smart healthcare coordination that anticipates needs and optimizes care outcomes.
            </p>
            <button
              onClick={handleAIHelper}
              className="w-full px-6 py-3 rounded-lg font-medium macos-button-elegant border"
              style={{
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--text-primary)',
                borderColor: 'var(--border-medium)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
              }}
            >
              AI Helper →
            </button>
          </div>
        </div>
      </section>

      {/* Essential Care Tools */}
      <section className="py-16 px-8 text-center bg-primary macos-section-elegant">
        <h2 className="text-3xl font-bold mb-4 text-primary">
          Essential Care Tools
        </h2>
        <p className="mb-12 text-secondary">
          Stay connected and organized with integrated care management tools.
        </p>

        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Shared Calendars */}
          <Link
            to="/shared-calendars"
            className="rounded-lg p-8 shadow-sm block macos-card-elegant bg-primary border border-light"
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 bg-accent"
            >
              <Calendar className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-4 text-primary">
              Shared Calendars
            </h3>
          </Link>

          {/* Secure Messaging */}
          <Link
            to="/secure-messaging"
            className="rounded-lg p-8 shadow-sm block macos-card-elegant bg-primary border border-light"
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 bg-accent"
            >
              <MessageSquare className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-4 text-primary">
              Secure Messaging
            </h3>
          </Link>

          {/* Task Management */}
          <Link
            to="/task-management"
            className="rounded-lg p-8 shadow-sm block macos-card-elegant bg-primary border border-light"
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 bg-accent"
            >
              <CheckSquare className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-4 text-primary">
              Task Management
            </h3>
          </Link>
        </div>
      </section>

      {/* Provider Cards Section - REAL DATA FROM DATABASE */}
      <section className="py-16 bg-primary macos-section-elegant">
        <div className="max-w-4xl mx-auto px-6">
          <h2 className="text-3xl font-bold mb-6 text-center text-primary">
            Featured Care Providers
          </h2>
          <p className="text-center mb-8 text-secondary">
            Meet our top-rated, verified healthcare professionals ready to provide exceptional care.
          </p>

          {loading ? (
            <div className="text-center py-16">
              <div className="max-w-sm mx-auto">
                <div className="flex justify-center mb-6">
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full border-2 border-transparent animate-spin border-t-primary border-r-primary"></div>
                    <div className="absolute inset-0 w-12 h-12 rounded-full border-2 opacity-20 border-primary"></div>
                  </div>
                </div>
                <h3 className="text-lg font-medium mb-2 text-primary">
                  Connecting to Provider Network
                </h3>
                <p className="text-sm mb-3 text-secondary">
                  Searching verified healthcare professionals in your area...
                </p>
                <div className="flex items-center justify-center gap-2 text-xs text-muted">
                  <span>•</span>
                  <span>Verifying credentials</span>
                  <span>•</span>
                  <span>Checking availability</span>
                  <span>•</span>
                  <span>Matching preferences</span>
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 bg-error-light">
                  <svg className="w-8 h-8 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-3 text-primary">
                  Unable to Load Providers
                </h3>
                <p className="text-base mb-6 text-secondary">
                  {error}
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="button-primary px-6 py-3 rounded-lg font-medium macos-button-elegant"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : caregivers.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {caregivers.map((caregiver) => (
                  <div
                    key={caregiver.id}
                    className="rounded-2xl p-8 transition-all duration-300 border cursor-pointer bg-primary border-light shadow-light hover:-translate-y-0.5 hover:shadow-medium"
                  >
                    {/* Header with Avatar and Verification */}
                    <div className="flex items-start gap-4 mb-4">
                      <div className="relative">
                        <div
                          className="w-14 h-14 rounded-full flex items-center justify-center bg-accent"
                        >
                          {caregiver.avatar_url ? (
                            <img
                              src={caregiver.avatar_url}
                              alt={caregiver.full_name || 'Provider'}
                              className="w-14 h-14 rounded-full object-cover"
                            />
                          ) : (
                            <Users className="w-7 h-7 text-accent" />
                          )}
                        </div>
                        {/* Verification Badge */}
                        <div className="absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center button-primary">
                          <Shield className="w-3 h-3 text-white" />
                        </div>
                      </div>

                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-1 text-primary">
                          {caregiver.full_name || 'Professional Caregiver'}
                        </h3>

                        {/* Rating Display */}
                        <div className="flex items-center gap-2 mb-2">
                          <div className="flex items-center">
                            {caregiver.average_rating ? [1, 2, 3, 4, 5].map((star) => (
                              <span key={star} className={`text-sm ${star <= caregiver.average_rating ? 'text-accent' : 'text-muted'}`}>
                                ★
                              </span>
                            )) : (
                              <span className="text-sm text-muted">Not rated</span>
                            )}
                          </div>
                          <span className="text-sm font-medium text-secondary">
                            {caregiver.average_rating ? `${caregiver.average_rating.toFixed(1)}★` : 'New provider'} {caregiver.reviews_count > 0 && `(${caregiver.reviews_count} reviews)`}
                          </span>
                        </div>

                        {/* Availability Indicator */}
                        {caregiver.availability_status && (
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${caregiver.availability_status === 'available' ? 'bg-accent' : 'bg-muted'}`}></div>
                            <span className={`text-sm font-medium ${caregiver.availability_status === 'available' ? 'text-accent' : 'text-muted'}`}>
                              {caregiver.availability_status === 'available' ? 'Available' : 'Busy'}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Bio */}
                    {caregiver.bio && (
                      <p className="text-sm mb-4 leading-relaxed text-secondary">
                        {caregiver.bio.length > 120 ? `${caregiver.bio.substring(0, 120)}...` : caregiver.bio}
                      </p>
                    )}
                    <div className="text-center mb-4">
                      {caregiver.location && (
                        <div className="flex items-center justify-center gap-1 mb-2 text-secondary">
                          <span>📍</span>
                          <span>{caregiver.location}</span>
                        </div>
                      )}
                      {caregiver.specialties && (
                        <div className="flex gap-2 justify-center flex-wrap">
                          {caregiver.specialties.slice(0, 2).map((specialty: string, index: number) => (
                            <span
                              key={index}
                              className="px-2 py-1 rounded text-sm bg-accent text-primary"
                            >
                              {specialty}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <Link
                      to={`/provider/caregiver/${caregiver.id}`}
                      className="button-primary w-full py-2 px-4 rounded-lg font-medium transition-colors block text-center hover:opacity-90"
                    >
                      View Profile
                    </Link>
                  </div>
                ))}
              </div>

              <div className="text-center mt-8">
                <Link
                  to="/caregivers"
                  className="button-primary px-6 py-3 rounded-lg font-medium transition-colors inline-block hover:opacity-90"
                >
                  View All Caregivers →
                </Link>
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 bg-accent">
                  <Search className="w-8 h-8 text-accent" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-primary">
                  Building Our Provider Network
                </h3>
                <p className="text-base mb-6 text-secondary">
                  We're carefully vetting and onboarding qualified care providers to ensure the highest quality of service.
                </p>
                <button
                  onClick={handleBrowseProviders}
                  className="button-primary px-6 py-3 rounded-lg font-medium macos-button-elegant"
                >
                  Join Our Provider Network
                </button>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Take Control Section */}
      <section className="py-16 px-8 text-center bg-primary">
        <h2 className="text-4xl font-bold mb-4 text-primary">
          Take <span className="text-accent">Control</span> of Your Care
        </h2>
        <p className="mb-8 text-secondary">
          Connect with care professionals.
        </p>

        <div className="flex gap-4 justify-center flex-wrap">
          <button
            onClick={handleGetStarted}
            className="button-primary px-6 py-3 rounded-lg font-medium transition-colors hover:opacity-90"
          >
            Get Started →
          </button>

          <button
            onClick={handleBrowseProviders}
            className="px-6 py-3 rounded-lg font-medium flex items-center gap-2 macos-button-elegant bg-primary text-primary border border-medium"
          >
            <Search className="w-4 h-4" />
            Browse Providers
          </button>
        </div>
      </section>

      {/* Footer - Enhanced Spacing & Alignment */}
      <footer
        className="py-20 px-8 bg-primary border-t border-light"
      >
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
            {/* Logo and Description - Enhanced */}
            <div className="lg:col-span-1">
              <div className="flex items-center gap-3 mb-6">
                <div
                  className="w-10 h-10 rounded-xl flex items-center justify-center shadow-sm button-primary"
                >
                  <span className="font-bold text-base text-white">CC</span>
                </div>
                <span className="font-bold text-2xl text-primary">Care Connector</span>
              </div>
              <p className="mb-6 text-base leading-relaxed max-w-md text-secondary">
                Modern healthcare coordination platform connecting families with verified care professionals through intelligent automation and secure collaboration tools.
              </p>
              <div className="flex flex-wrap gap-6">
                <div className="flex items-center gap-2 text-sm text-secondary">
                  <Shield className="w-4 h-4 text-accent" />
                  <span className="font-medium">HIPAA Compliant</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-secondary">
                  <UserCheck className="w-4 h-4 text-accent" />
                  <span className="font-medium">Verified Providers</span>
                </div>
              </div>
            </div>

            {/* Services - Enhanced */}
            <div>
              <h3 className="font-semibold mb-6 text-lg text-primary">Services</h3>
              <ul className="space-y-4 text-secondary">
                <li>
                  <Link
                    to="/caregivers"
                    className="transition-all duration-200 hover:translate-x-1 text-secondary hover:text-primary"
                  >
                    Find Care
                  </Link>
                </li>
                <li>
                  <Link
                    to="/care-groups"
                    className="transition-all duration-200 hover:translate-x-1 text-secondary hover:text-primary"
                  >
                    Care Groups
                  </Link>
                </li>
              </ul>
            </div>

            {/* Learn More - Enhanced */}
            <div>
              <h3 className="font-semibold mb-6 text-lg text-primary">Learn More</h3>
              <ul className="space-y-4 text-secondary">
                <li>
                  <Link
                    to="/how-it-works"
                    className="transition-all duration-200 hover:translate-x-1 text-secondary hover:text-primary"
                  >
                    How It Works
                  </Link>
                </li>
                <li>
                  <Link
                    to="/features"
                    className="transition-all duration-200 hover:translate-x-1 text-secondary hover:text-primary"
                  >
                    Features
                  </Link>
                </li>
              </ul>
            </div>

            {/* Legal & Compliance - New Section */}
            <div>
              <h3 className="font-semibold mb-6 text-lg text-primary">Legal & Compliance</h3>
              <ul className="space-y-4 text-secondary">
                <li>
                  <Link
                    to="/privacy-policy"
                    className="transition-all duration-200 hover:translate-x-1 text-secondary hover:text-primary"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    to="/terms-of-service"
                    className="transition-all duration-200 hover:translate-x-1 text-secondary hover:text-primary"
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    to="/hipaa-notice"
                    className="transition-all duration-200 hover:translate-x-1 text-secondary hover:text-primary"
                  >
                    HIPAA Notice
                  </Link>
                </li>
                <li>
                  <Link
                    to="/provider-portal"
                    className="transition-all duration-200 hover:translate-x-1 text-secondary hover:text-primary"
                  >
                    Provider Portal
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div
            className="pt-8 text-center border-t border-light text-secondary"
          >
            <p className="text-sm">© {new Date().getFullYear()} Care Connector. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </main>
  )
}
